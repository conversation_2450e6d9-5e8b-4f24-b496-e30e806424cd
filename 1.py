import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def adjusted_r2_score(y_true, y_pred, n_features):
    """计算调整后的R²"""
    r2 = r2_score(y_true, y_pred)
    n = len(y_true)  # 样本量
    k = n_features  # 自变量个数

    if n - k - 1 <= 0:
        return float('-inf')  # 避免除零错误

    adjusted_r2 = 1 - ((1 - r2) * (n - 1) / (n - k - 1))
    return adjusted_r2


def load_data(filename):
    """加载Excel数据并分离成5组（确保每组都保留第一行数据）"""
    data = pd.read_excel(filename, header=None)

    groups = []
    for i in range(5):
        x_col = i * 2
        y_col = i * 2 + 1

        if x_col >= data.shape[1] or y_col >= data.shape[1]:
            groups.append((np.array([]), np.array([])))
            continue

        x_data = data.iloc[:, x_col].values
        y_data = data.iloc[:, y_col].values

        # 过滤有效数据（非缺失、数值型）
        mask = (~pd.isna(x_data)) & (~pd.isna(y_data))
        str_mask = [True if (str(x).replace('.', '', 1).isdigit() and
                             str(y).replace('.', '', 1).isdigit())
                    else False for x, y in zip(x_data, y_data)]
        final_mask = np.array(mask) & np.array(str_mask)

        # 强制保留第一行有效数据
        if len(final_mask) > 0 and not pd.isna(x_data[0]) and not pd.isna(y_data[0]):
            final_mask[0] = True

        x_clean = x_data[final_mask].astype(float)
        y_clean = y_data[final_mask].astype(float)
        groups.append((x_clean, y_clean))

    return groups


def find_optimal_degree(x, y, max_degree=20):
    """交叉验证找最佳多项式次数（强制过原点），返回RMSE"""
    degrees = range(1, max_degree + 1)
    cv_scores = []

    for degree in degrees:
        poly_reg = Pipeline([
            ('poly', PolynomialFeatures(degree=degree, include_bias=False)),
            ('ridge', Ridge(alpha=1.0, fit_intercept=False))
        ])

        # 计算MSE然后转换为RMSE
        mse_scores = cross_val_score(poly_reg, x.reshape(-1, 1), y, cv=5, scoring='neg_mean_squared_error')
        rmse_scores = np.sqrt(-mse_scores)  # 先转为正MSE，再开方得到RMSE
        cv_scores.append(rmse_scores.mean())  # 取平均RMSE

    optimal_degree = degrees[np.argmin(cv_scores)]
    return optimal_degree, cv_scores


def evaluate_polynomial(x, y, degree):
    """用指定次数多项式拟合数据（强制过原点）"""
    model = Pipeline([
        ('poly', PolynomialFeatures(degree=degree, include_bias=False)),
        ('linear', LinearRegression(fit_intercept=False))
    ])
    model.fit(x.reshape(-1, 1), y)

    y_pred = model.predict(x.reshape(-1, 1))
    mse = mean_squared_error(y, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y, y_pred)
    adj_r2 = adjusted_r2_score(y, y_pred, degree)

    return model, y_pred, mse, rmse, r2, adj_r2


def extract_polynomial_expression(model, degree):
    """提取多项式表达式（过原点，无截距项）"""
    reg_model = model.named_steps.get('ridge') or model.named_steps.get('linear')
    coefficients = reg_model.coef_

    expression = "y = "
    first_term = True
    for i, coef in enumerate(coefficients):
        power = i + 1
        if first_term:
            expression += f"{coef:.6f}x^{power}" if coef >= 0 else f"-{abs(coef):.6f}x^{power}"
            first_term = False
        else:
            expression += f" + {coef:.6f}x^{power}" if coef >= 0 else f" - {abs(coef):.6f}x^{power}"

    return expression, coefficients


def display_group_polynomial(model, degree, group_num):
    """显示指定组多项式信息"""
    expression, coefficients = extract_polynomial_expression(model, degree)
    return expression


def main():
    print("=== 多项式次数确定与验证分析（强制过原点） ===")
    print("注意：模型已被修改为强制满足x=0时y=0\n")

    # 加载数据（替换为你的Excel路径）
    groups = load_data('D:\\桌面\\1.xlsx')

    print("数据加载完成，各组数据点数量（包含第一行）：")
    for i, (x, y) in enumerate(groups):
        print(f"第{i + 1}组: {len(x)} 个数据点")

    # 前四组找各自最佳次数
    print("\n=== 确定前四组数据各自的最佳多项式次数 ===")
    optimal_degrees = []
    all_cv_scores = []  # 这里存储的是RMSE
    group_models = []
    group_expressions = []

    for i in range(4):
        x, y = groups[i]
        if len(x) > 10:
            optimal_degree, cv_scores = find_optimal_degree(x, y, max_degree=20)
            optimal_degrees.append(optimal_degree)
            all_cv_scores.append(cv_scores)
            print(f"第{i + 1}组数据最佳多项式次数: {optimal_degree}")

            # 训练并保存当前组模型
            model = Pipeline([
                ('poly', PolynomialFeatures(degree=optimal_degree, include_bias=False)),
                ('linear', LinearRegression(fit_intercept=False))
            ])
            model.fit(x.reshape(-1, 1), y)
            group_models.append(model)

            # 保存表达式
            expr = display_group_polynomial(model, optimal_degree, i + 1)
            group_expressions.append(expr)
        else:
            print(f"第{i + 1}组数据点不足，跳过")
            optimal_degrees.append(None)
            group_models.append(None)
            group_expressions.append("数据不足，无法生成表达式")

    # 打印多项式次数与各组RMSE对照表
    print("\n=== 多项式次数与各组RMSE对照表 ===")
    max_degree = 20
    print(f"{'多项式次数':<10} {'第1组RMSE':<15} {'第2组RMSE':<15} {'第3组RMSE':<15} {'第4组RMSE':<15}")
    print("-" * 70)
    for degree in range(1, max_degree + 1):
        row = [f"{degree:<10}"]
        for i in range(4):
            if i < len(all_cv_scores) and (degree - 1) < len(all_cv_scores[i]):
                row.append(f"{all_cv_scores[i][degree - 1]:<15.6f}")
            else:
                row.append(f"{'N/A':<15}")
        print("".join(row))

    # 确定第五组验证用的最终次数（前四组有效次数的均值）
    valid_degrees = [d for d in optimal_degrees if d is not None]
    if valid_degrees:
        final_degree = int(np.round(np.mean(valid_degrees)))
        # 重点：明确打印预测数据（第五组）用的最佳次数
        print(f"\n预测数据（第五组）使用的最佳多项式次数: {final_degree}")
        print(f"\n各组最佳次数: {optimal_degrees}")
        print(f"最终用于第五组验证的多项式次数: {final_degree}")
    else:
        print("无法确定最佳多项式次数，程序终止")
        return

    # 第五组数据处理：用最终次数直接拟合
    print(f"\n=== 用 {final_degree} 次多项式直接拟合第五组数据 ===")

    # 第五组作为独立数据集
    x_test, y_test = groups[4]

    if len(x_test) > 0:
        # 直接用第五组数据和最终次数拟合模型
        model, y_pred, mse, rmse, r2, adj_r2 = evaluate_polynomial(
            x_test, y_test, final_degree
        )

        # 获取第五组的表达式
        fifth_expr, _ = extract_polynomial_expression(model, final_degree)
        group_expressions.append(fifth_expr)

        # 打印第五组验证结果表格
        print("\n=== 第五组数据拟合结果 ===")
        print(f"{'指标':<20} {'值':<15}")
        print("-" * 35)
        print(f"{'数据点数量':<20} {len(x_test):<15}")
        print(f"{'均方误差 (MSE)':<20} {mse:.6f}")
        print(f"{'均方根误差 (RMSE)':<20} {rmse:.6f}")
        print(f"{'决定系数 (R²)':<20} {r2:.6f}")
        print(f"{'调整后决定系数':<20} {adj_r2:.6f}")

        # 验证x=0时预测值
        zero_pred = model.predict(np.array([[0]]))[0]
        print(f"{'x=0时预测y值':<20} {zero_pred:.10f}")

        # 显示第五组多项式表达式
        display_polynomial_info(model, final_degree, f"第五组{final_degree}次多项式")

        # 打印各组多项式函数表达式对照表
        print("\n=== 各组多项式函数表达式对照表 ===")
        print(f"{'组号':<5} {'多项式函数表达式'}")
        print("-" * 100)
        for i in range(5):
            print(f"{i + 1:<5} {group_expressions[i]}")

        # 绘图
        plt.figure(figsize=(15, 10))

        # 子图1：各组交叉验证结果（RMSE）
        plt.subplot(2, 3, 1)
        for i, cv_scores in enumerate(all_cv_scores):
            plt.plot(range(1, len(cv_scores) + 1), cv_scores, 'o-', label=f'第{i + 1}组')
        plt.xlabel('多项式次数')
        plt.ylabel('交叉验证RMSE')
        plt.title('各组数据的交叉验证结果')
        plt.legend()
        plt.grid(True)

        # 子图2-5：前四组拟合结果（用各自最佳次数）
        for i in range(4):
            plt.subplot(2, 3, i + 2)
            x, y = groups[i]
            if len(x) > 0 and optimal_degrees[i] is not None and group_models[i] is not None:
                degree = optimal_degrees[i]
                model = group_models[i]

                x_min = min(x.min(), 0)
                x_max = x.max()
                x_smooth = np.linspace(x_min, x_max, 100)
                y_smooth = model.predict(x_smooth.reshape(-1, 1))

                plt.scatter(x, y, alpha=0.6, s=20, label='实际数据')
                plt.scatter(x[0], y[0], color='green', s=50, label='第一行数据')
                plt.plot(x_smooth, y_smooth, 'r-', label=f'{degree}次多项式')
                plt.scatter(0, 0, color='black', s=80, marker='*', label='原点(0,0)')
                plt.xlabel('X')
                plt.ylabel('Y')
                plt.title(f'第{i + 1}组数据拟合 (次数={degree})')
                plt.legend()
                plt.grid(True)

        # 子图6：第五组拟合结果（只包含实际数据和拟合曲线）
        plt.subplot(2, 3, 6)
        x, y = groups[4]
        if len(x) > 0:
            x_min = min(x.min(), 0)
            x_max = x.max()
            x_smooth = np.linspace(x_min, x_max, 100)
            y_smooth = model.predict(x_smooth.reshape(-1, 1))

            plt.scatter(x, y, alpha=0.6, s=20, label='实际数据')
            plt.scatter(x[0], y[0], color='green', s=50, label='第一行数据')
            plt.plot(x_smooth, y_smooth, 'r-', label=f'{final_degree}次多项式')
            plt.scatter(0, 0, color='black', s=80, marker='*', label='原点(0,0)')
            plt.xlabel('X')
            plt.ylabel('Y')
            plt.title(f'第5组数据拟合 (R²={r2:.3f})')
            plt.legend()
            plt.grid(True)

        plt.tight_layout()
        plt.savefig('polynomial_analysis_origin.png', dpi=300, bbox_inches='tight')
        plt.show()

    else:
        print("第五组数据为空，无法验证")


def display_polynomial_info(model, degree, model_name="多项式"):
    """显示多项式详细信息"""
    print(f"\n=== {model_name}回归表达式 (强制过原点) ===")
    expression, coefficients = extract_polynomial_expression(model, degree)

    print(f"数学表达式:\n  {expression}")
    print("\n系数详情:")
    for i, coef in enumerate(coefficients):
        print(f"  x^{i + 1}系数: {coef:.6f}")

    max_coef = max(abs(c) for c in coefficients)
    min_coef = min(abs(c) for c in coefficients)
    print(f"\n系数分析:\n  最大系数绝对值: {max_coef:.6f}\n  最小系数绝对值: {min_coef:.6f}")

    if hasattr(model.named_steps.get('ridge', None), 'alpha'):
        print(f"  Ridge正则化参数α: {model.named_steps['ridge'].alpha}")

    return expression


if __name__ == "__main__":
    main()
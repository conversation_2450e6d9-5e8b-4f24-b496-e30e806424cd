import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def adjusted_r2_score(y_true, y_pred, n_features):
    """
    计算调整后的R²

    参数:
    y_true: 真实值
    y_pred: 预测值
    n_features: 特征数量（多项式次数）

    公式: 调整后R² = 1 - [(1-R²) × (n-1) / (n-k-1)]
    其中 n 是样本量，k 是自变量个数
    """
    r2 = r2_score(y_true, y_pred)
    n = len(y_true)  # 样本量
    k = n_features   # 自变量个数

    if n - k - 1 <= 0:
        return float('-inf')  # 避免除零错误

    adjusted_r2 = 1 - ((1 - r2) * (n - 1) / (n - k - 1))
    return adjusted_r2

def load_data(filename):
    """加载数据并分离成5组"""
    data = pd.read_csv(filename, header=None)

    # 分离成5组数据，每组包含x和y值
    groups = []
    for i in range(5):
        x_col = i * 2
        y_col = i * 2 + 1

        # 获取x和y数据，排除第一行（全零行）和缺失值
        x_data = data.iloc[1:, x_col].values
        y_data = data.iloc[1:, y_col].values

        # 创建掩码来过滤有效数据（非零且非缺失）
        mask = (~pd.isna(x_data)) & (~pd.isna(y_data)) & (x_data != 0) & (y_data != 0)

        # 过滤掉包含"--"的数据
        str_mask = []
        for j in range(len(x_data)):
            try:
                float(x_data[j])
                float(y_data[j])
                str_mask.append(True)
            except:
                str_mask.append(False)

        str_mask = np.array(str_mask)
        final_mask = mask & str_mask

        if np.sum(final_mask) > 0:
            x_clean = x_data[final_mask].astype(float)
            y_clean = y_data[final_mask].astype(float)
            groups.append((x_clean, y_clean))
        else:
            groups.append((np.array([]), np.array([])))

    return groups

def find_optimal_degree(x, y, max_degree=10):
    """使用交叉验证找到最佳多项式次数"""
    degrees = range(1, max_degree + 1)
    cv_scores = []

    for degree in degrees:
        # 创建多项式回归管道 (使用Ridge回归防止过拟合)
        poly_reg = Pipeline([
            ('poly', PolynomialFeatures(degree=degree)),
            ('ridge', Ridge(alpha=1.0))  # 使用Ridge回归替代普通线性回归
        ])

        # 使用交叉验证评估模型
        scores = cross_val_score(poly_reg, x.reshape(-1, 1), y, cv=5, scoring='neg_mean_squared_error')
        cv_scores.append(-scores.mean())  # 转换为正的MSE

    # 找到最佳次数
    optimal_degree = degrees[np.argmin(cv_scores)]
    return optimal_degree, cv_scores

def evaluate_polynomial(x_train, y_train, x_test, y_test, degree):
    """评估指定次数的多项式在测试集上的性能"""
    # 训练模型 (使用Ridge回归)
    poly_reg = Pipeline([
        ('poly', PolynomialFeatures(degree=degree)),
        ('ridge', Ridge(alpha=1.0))  # 使用Ridge回归防止过拟合
    ])

    poly_reg.fit(x_train.reshape(-1, 1), y_train)

    # 预测
    y_pred = poly_reg.predict(x_test.reshape(-1, 1))

    # 计算评估指标
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)                    # 普通R²
    adj_r2 = adjusted_r2_score(y_test, y_pred, degree)  # 调整后R²

    return poly_reg, y_pred, mse, r2, adj_r2

def extract_polynomial_expression(model, degree):
    """提取多项式回归的数学表达式"""
    # 获取Ridge回归的系数
    ridge_model = model.named_steps['ridge']
    coefficients = ridge_model.coef_
    intercept = ridge_model.intercept_

    # 构建表达式字符串
    expression = f"y = {intercept:.6f}"

    for i, coef in enumerate(coefficients):
        if i == 0:  # 一次项
            if coef >= 0:
                expression += f" + {coef:.6f}x"
            else:
                expression += f" - {abs(coef):.6f}x"
        else:  # 高次项
            power = i + 1
            if coef >= 0:
                expression += f" + {coef:.6f}x^{power}"
            else:
                expression += f" - {abs(coef):.6f}x^{power}"

    return expression, coefficients, intercept

def display_polynomial_info(model, degree, model_name="多项式"):
    """显示多项式的详细信息"""
    print(f"\n=== {model_name}回归表达式 ===")

    expression, coefficients, intercept = extract_polynomial_expression(model, degree)

    print(f"数学表达式:")
    print(f"  {expression}")

    print(f"\n系数详情:")
    print(f"  截距项: {intercept:.6f}")
    for i, coef in enumerate(coefficients):
        power = i + 1
        print(f"  x^{power}系数: {coef:.6f}")

    # 分析系数大小
    print(f"\n系数分析:")
    max_coef = max(abs(c) for c in coefficients)
    min_coef = min(abs(c) for c in coefficients)
    print(f"  最大系数绝对值: {max_coef:.6f}")
    print(f"  最小系数绝对值: {min_coef:.6f}")
    print(f"  系数范围比: {max_coef/min_coef:.2f}" if min_coef > 0 else "  系数范围比: 无穷大")

    # Ridge回归特有信息
    if hasattr(model.named_steps.get('ridge', None), 'alpha'):
        alpha = model.named_steps['ridge'].alpha
        print(f"  Ridge正则化参数α: {alpha}")
        print(f"  正则化强度: {'强' if alpha >= 10 else '中等' if alpha >= 1 else '轻微'}")

    return expression

def main():
    print("=== 多项式次数确定与验证分析 ===\n")

    # 加载数据
    groups = load_data('b2.csv')

    print("数据加载完成，各组数据点数量：")
    for i, (x, y) in enumerate(groups):
        print(f"第{i+1}组: {len(x)} 个数据点")

    # 使用前四组数据确定最佳多项式次数
    print("\n=== 使用前四组数据确定最佳多项式次数 ===")

    optimal_degrees = []
    all_cv_scores = []

    for i in range(4):  # 前四组数据
        x, y = groups[i]
        if len(x) > 10:  # 确保有足够的数据点
            optimal_degree, cv_scores = find_optimal_degree(x, y, max_degree=30)
            optimal_degrees.append(optimal_degree)
            all_cv_scores.append(cv_scores)
            print(f"第{i+1}组数据最佳多项式次数: {optimal_degree}")
        else:
            print(f"第{i+1}组数据点不足，跳过")

    # 确定最终的多项式次数（取众数或平均值）
    if optimal_degrees:
        # 计算平均次数并四舍五入
        final_degree = int(np.round(np.mean(optimal_degrees)))
        print(f"\n各组最佳次数: {optimal_degrees}")
        print(f"最终确定的多项式次数: {final_degree}")
    else:
        print("无法确定最佳多项式次数")
        return

    # 在第五组数据上验证准确性
    print(f"\n=== 在第五组数据上验证 {final_degree} 次多项式的准确性 ===")

    # 合并前四组数据作为训练集
    all_x_train = []
    all_y_train = []

    for i in range(4):
        x, y = groups[i]
        if len(x) > 0:
            all_x_train.extend(x)
            all_y_train.extend(y)

    all_x_train = np.array(all_x_train)
    all_y_train = np.array(all_y_train)

    # 第五组数据作为测试集
    x_test, y_test = groups[4]

    if len(x_test) > 0:
        # 评估模型
        model, y_pred, mse, r2, adj_r2 = evaluate_polynomial(
            all_x_train, all_y_train, x_test, y_test, final_degree
        )

        print(f"测试集数据点数量: {len(x_test)}")
        print(f"均方误差 (MSE): {mse:.4f}")
        print(f"决定系数 (R²): {r2:.4f}")
        print(f"调整后决定系数 (R²): {adj_r2:.4f}")
        print(f"均方根误差 (RMSE): {np.sqrt(mse):.4f}")

        # 显示最终的多项式表达式
        expression = display_polynomial_info(model, final_degree, f"{final_degree}次多项式")

        # 绘制结果
        plt.figure(figsize=(15, 10))

        # 子图1: 各组数据的交叉验证结果
        plt.subplot(2, 3, 1)
        for i, cv_scores in enumerate(all_cv_scores):
            plt.plot(range(1, len(cv_scores) + 1), cv_scores, 'o-', label=f'第{i+1}组')
        plt.xlabel('多项式次数')
        plt.ylabel('交叉验证MSE')
        plt.title('各组数据的交叉验证结果')
        plt.legend()
        plt.grid(True)

        # 子图2-5: 前四组数据的拟合结果
        for i in range(4):
            plt.subplot(2, 3, i + 2)
            x, y = groups[i]
            if len(x) > 0:
                # 拟合当前组数据
                poly_reg = Pipeline([
                    ('poly', PolynomialFeatures(degree=final_degree)),
                    ('linear', LinearRegression())
                ])
                poly_reg.fit(x.reshape(-1, 1), y)

                # 生成平滑的预测曲线
                x_smooth = np.linspace(x.min(), x.max(), 100)
                y_smooth = poly_reg.predict(x_smooth.reshape(-1, 1))

                plt.scatter(x, y, alpha=0.6, s=20, label='实际数据')
                plt.plot(x_smooth, y_smooth, 'r-', label=f'{final_degree}次多项式')
                plt.xlabel('X')
                plt.ylabel('Y')
                plt.title(f'第{i+1}组数据拟合')
                plt.legend()
                plt.grid(True)

        # 子图6: 第五组数据的验证结果
        plt.subplot(2, 3, 6)

        # 生成平滑的预测曲线
        x_smooth = np.linspace(x_test.min(), x_test.max(), 100)
        y_smooth = model.predict(x_smooth.reshape(-1, 1))

        plt.scatter(x_test, y_test, alpha=0.6, s=20, label='实际数据', color='blue')
        plt.scatter(x_test, y_pred, alpha=0.6, s=20, label='预测数据', color='red')
        plt.plot(x_smooth, y_smooth, 'r-', label=f'{final_degree}次多项式', linewidth=2)
        plt.xlabel('X')
        plt.ylabel('Y')
        plt.title(f'第5组数据验证 (R²={r2:.3f})')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        plt.savefig('polynomial_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 详细的预测结果
        print(f"\n=== 详细预测结果 (前10个点) ===")
        print("序号\t实际值\t\t预测值\t\t误差")
        print("-" * 50)
        for i in range(min(10, len(x_test))):
            error = abs(y_test[i] - y_pred[i])
            print(f"{i+1}\t{y_test[i]:.4f}\t\t{y_pred[i]:.4f}\t\t{error:.4f}")

    else:
        print("第五组数据为空，无法进行验证")

if __name__ == "__main__":
    main()